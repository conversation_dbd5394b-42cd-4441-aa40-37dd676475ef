# Enhanced PPTX Parser Implementation

## 🎯 Overview

This document describes the enhanced PPTX parser implementation that converts PowerPoint files (.pptx) into a standardized JSON structure. The parser extracts and categorizes slide elements into texts, images, shapes (as SVG), and tables.

## 📋 JSON Output Structure

### Core Interfaces

```typescript
interface PptxSlide {
  slideNumber: number;
  texts: SlideText[];
  images: SlideImage[];
  shapes: SlideShape[];  // includes both single shapes and group shapes (svg string)
  tables: SlideTable[];
}

interface SlideText {
  text: string;
  x: number;
  y: number;
  fontSize?: number;
  fontFamily?: string;
  color?: string;
}

interface SlideImage {
  src: string; // base64
  x: number;
  y: number;
  width: number;
  height: number;
}

interface SlideShape {
  svg: string; // SVG string, e.g., <rect ... /> or <g>...</g> for groups
  x: number;
  y: number;
  width: number;
  height: number;
  text?: string;
  fill?: string;
  border?: string;
}

interface SlideTable {
  x: number;
  y: number;
  width: number;
  height: number;
  rows: string[][]; // Each element is a row
}
```

## 🧩 Element Mapping

| Element Type | XML Source | JSON Output | Notes |
|--------------|------------|-------------|-------|
| Text | `p:sp` with `p:txBody` | `SlideText` | Preserves content, position, font, color |
| Shape | `p:sp` with `a:prstGeom` | `SlideShape` with SVG | Converted to SVG string |
| Group | `p:grpSp` | `SlideShape` with `<g>...</g>` | Wrapped in SVG group element |
| Image | `p:pic` | `SlideImage` | Base64 from `ppt/media/` |
| Table | `p:graphicFrame` with `a:tbl` | `SlideTable` | Mapped to row/cell array |

## 🔧 Core Parsing Functions

### 1. parseTextFromSp()
Extracts text content from shape elements.

```typescript
const parseTextFromSp = (shape: any): string | null => {
  const textBody = shape["p:txBody"]?.[0];
  if (!textBody) return null;

  const paragraphs = textBody["a:p"] || [];
  const texts: string[] = [];

  paragraphs.forEach((p: any) => {
    const runs = p["a:r"] || [];
    runs.forEach((r: any) => {
      const text = r["a:t"]?.[0];
      if (text) texts.push(text);
    });
  });

  return texts.length > 0 ? texts.join(" ") : null;
};
```

### 2. parseShapeToSvg()
Converts shape properties to SVG format.

```typescript
const parseShapeToSvg = (shape: any): SlideShape | null => {
  // Extract position and dimensions
  const spPr = shape["p:spPr"]?.[0];
  const xfrm = spPr?.["a:xfrm"]?.[0];
  const off = xfrm?.["a:off"]?.[0]?.$;
  const ext = xfrm?.["a:ext"]?.[0]?.$;

  // Get shape type from preset geometry
  const prstGeom = spPr["a:prstGeom"]?.[0];
  const shapeType = prstGeom?.$?.prst || "rect";

  // Generate SVG using existing generateSvgPath function
  const svg = generateSvgPath(shapeType, width, height);

  return {
    svg,
    x, y, width, height,
    text: parseTextFromSp(shape),
    fill, border
  };
};
```

### 3. parseGroupShape()
Handles grouped elements by recursively processing children.

```typescript
const parseGroupShape = (group: any): SlideShape | null => {
  const children: string[] = [];

  // Process child shapes
  const shapeList = group["p:sp"] || [];
  shapeList.forEach((sp: any) => {
    const shapeData = parseShapeToSvg(sp);
    if (shapeData) {
      children.push(`<g transform="translate(${shapeData.x},${shapeData.y})" fill="${shapeData.fill}">${shapeData.svg}</g>`);
    }
  });

  return {
    svg: `<g>${children.join("")}</g>`,
    x, y, width, height
  };
};
```

### 4. parseImageElement()
Extracts image references and dimensions.

```typescript
const parseImageElement = (pic: any): SlideImage | null => {
  const blipFill = pic["p:blipFill"]?.[0];
  const blip = blipFill?.["a:blip"]?.[0];
  const embedId = blip?.$?.["r:embed"];

  // Extract position and size
  const xfrm = pic["p:spPr"]?.[0]?.["a:xfrm"]?.[0];
  // ... position extraction logic

  return {
    src: "", // Will be filled with base64 data later
    x, y, width, height
  };
};
```

### 5. parseTableElement()
Converts table structures to row/cell arrays.

```typescript
const parseTableElement = (frame: any): SlideTable | null => {
  const graphic = frame["a:graphic"]?.[0];
  const tbl = graphic?.["a:graphicData"]?.[0]?.["a:tbl"]?.[0];
  
  const rows = tbl["a:tr"] || [];
  const table: string[][] = [];

  rows.forEach((row: any) => {
    const cells = row["a:tc"] || [];
    const rowData: string[] = [];

    cells.forEach((cell: any) => {
      // Extract text from each cell
      const txBody = cell["a:txBody"]?.[0];
      // ... text extraction logic
      rowData.push(cellText.trim());
    });

    table.push(rowData);
  });

  return { x, y, width, height, rows: table };
};
```

## 🚀 Enhanced Features

### 1. Standardized JSON Output
- Consistent structure across all element types
- Position coordinates converted from EMU to pixels
- SVG-based shape rendering for scalability

### 2. Comprehensive Element Support
- **Text**: Full text extraction with formatting metadata
- **Shapes**: 15+ shape types converted to SVG paths
- **Groups**: Nested elements properly handled with SVG groups
- **Images**: Base64 encoding with proper relationship mapping
- **Tables**: Full table structure with cell-by-cell content

### 3. Improved Image Handling
- Relationship parsing for accurate image mapping
- Base64 conversion for embedded display
- Proper dimension extraction

### 4. SVG Shape Generation
Supports multiple shape types:
- Basic: rectangle, circle, triangle, diamond
- Arrows: left, right, up, down
- Complex: star, heart, hexagon, plus
- Flowchart: process, decision

## 📁 File Structure

```
src/lib/pptxParser.ts          # Main parser implementation
src/app/(tools)/pptx-enhanced/ # Demo page for testing
```

## 🧪 Testing

Access the enhanced parser demo at `/pptx-enhanced` to:
1. Upload PPTX files
2. View parsed results in both preview and JSON formats
3. Download standardized JSON output
4. Test with various slide layouts and element types

## 🔄 Migration from Old Parser

The enhanced parser maintains backward compatibility while providing:
- More accurate element extraction
- Standardized output format
- Better error handling
- Improved performance

## 📊 Performance Considerations

- Efficient XML parsing with xml2js
- Optimized image extraction from ZIP archives
- Minimal memory footprint for large presentations
- Streaming-friendly architecture for future enhancements

## 🎨 Usage Example

```typescript
import { parsePptx } from '@/lib/pptxParser';

const handleFileUpload = async (file: File) => {
  const arrayBuffer = await file.arrayBuffer();
  const result = await parsePptx(arrayBuffer);
  
  console.log(`Parsed ${result.slides.length} slides`);
  result.slides.forEach(slide => {
    console.log(`Slide ${slide.slideNumber}:`);
    console.log(`- ${slide.texts.length} texts`);
    console.log(`- ${slide.images.length} images`);
    console.log(`- ${slide.shapes.length} shapes`);
    console.log(`- ${slide.tables.length} tables`);
  });
};
```

This enhanced implementation provides a robust foundation for PPTX processing with standardized output suitable for various applications including slide editors, content analyzers, and presentation converters.

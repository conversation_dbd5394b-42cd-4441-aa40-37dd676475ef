"use client";

import React, { useState } from "react";
import { parsePptx, PptxSlide, PptxParseResult } from "@/lib/pptxParser";
import { Button } from "@/components/ui/Button";
import {
  Upload,
  Download,
  FileText,
  Image as ImageIcon,
  Square,
  Table,
  Eye,
  Code,
} from "lucide-react";
import { toast } from "sonner";

export default function PptxEnhancedDemo() {
  const [parseResult, setParseResult] = useState<PptxParseResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedSlide, setSelectedSlide] = useState<number>(0);
  const [viewMode, setViewMode] = useState<"preview" | "json">("preview");

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.name.toLowerCase().endsWith('.pptx')) {
      toast.error("Please select a PPTX file");
      return;
    }

    setIsLoading(true);
    try {
      const arrayBuffer = await file.arrayBuffer();
      const result = await parsePptx(arrayBuffer);
      setParseResult(result);
      setSelectedSlide(0);
      toast.success(`Successfully parsed ${result.slides.length} slides`);
    } catch (error) {
      console.error("Error parsing PPTX:", error);
      toast.error("Failed to parse PPTX file");
    } finally {
      setIsLoading(false);
    }
  };

  const downloadJSON = () => {
    if (!parseResult) return;
    
    const dataStr = JSON.stringify(parseResult, null, 2);
    const dataBlob = new Blob([dataStr], { type: "application/json" });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement("a");
    link.href = url;
    link.download = "pptx-parsed-data.json";
    link.click();
    URL.revokeObjectURL(url);
  };

  const currentSlide = parseResult?.slides[selectedSlide];

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Enhanced PPTX Parser Demo
          </h1>
          <p className="text-gray-600 mb-6">
            Upload a PPTX file to see the enhanced parsing with standardized JSON output including texts, images, shapes (as SVG), and tables.
          </p>

          {/* Upload Section */}
          <div className="flex items-center gap-4">
            <label className="relative">
              <input
                type="file"
                accept=".pptx"
                onChange={handleFileUpload}
                className="hidden"
                disabled={isLoading}
              />
              <Button disabled={isLoading} className="flex items-center gap-2">
                <Upload className="w-4 h-4" />
                {isLoading ? "Parsing..." : "Upload PPTX"}
              </Button>
            </label>

            {parseResult && (
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  onClick={downloadJSON}
                  className="flex items-center gap-2"
                >
                  <Download className="w-4 h-4" />
                  Download JSON
                </Button>
                
                <div className="flex items-center gap-2 ml-4">
                  <Button
                    variant={viewMode === "preview" ? "default" : "outline"}
                    onClick={() => setViewMode("preview")}
                    size="sm"
                  >
                    <Eye className="w-4 h-4 mr-1" />
                    Preview
                  </Button>
                  <Button
                    variant={viewMode === "json" ? "default" : "outline"}
                    onClick={() => setViewMode("json")}
                    size="sm"
                  >
                    <Code className="w-4 h-4 mr-1" />
                    JSON
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>

        {parseResult && (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Slide Navigation */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm p-4">
                <h3 className="font-semibold text-gray-900 mb-4">
                  Slides ({parseResult.slides.length})
                </h3>
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {parseResult.slides.map((slide, index) => (
                    <button
                      key={index}
                      onClick={() => setSelectedSlide(index)}
                      className={`w-full text-left p-3 rounded-lg border transition-colors ${
                        selectedSlide === index
                          ? "bg-blue-50 border-blue-200"
                          : "bg-gray-50 border-gray-200 hover:bg-gray-100"
                      }`}
                    >
                      <div className="font-medium">Slide {slide.slideNumber}</div>
                      <div className="text-sm text-gray-500 mt-1">
                        {slide.texts.length} texts, {slide.images.length} images,{" "}
                        {slide.shapes.length} shapes, {slide.tables.length} tables
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Main Content */}
            <div className="lg:col-span-3">
              {viewMode === "preview" ? (
                <div className="space-y-6">
                  {/* Slide Overview */}
                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-4">
                      Slide {currentSlide?.slideNumber} Overview
                    </h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center p-4 bg-blue-50 rounded-lg">
                        <FileText className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                        <div className="font-semibold text-blue-900">
                          {currentSlide?.texts.length || 0}
                        </div>
                        <div className="text-sm text-blue-600">Texts</div>
                      </div>
                      <div className="text-center p-4 bg-green-50 rounded-lg">
                        <ImageIcon className="w-8 h-8 text-green-600 mx-auto mb-2" />
                        <div className="font-semibold text-green-900">
                          {currentSlide?.images.length || 0}
                        </div>
                        <div className="text-sm text-green-600">Images</div>
                      </div>
                      <div className="text-center p-4 bg-purple-50 rounded-lg">
                        <Square className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                        <div className="font-semibold text-purple-900">
                          {currentSlide?.shapes.length || 0}
                        </div>
                        <div className="text-sm text-purple-600">Shapes</div>
                      </div>
                      <div className="text-center p-4 bg-orange-50 rounded-lg">
                        <Table className="w-8 h-8 text-orange-600 mx-auto mb-2" />
                        <div className="font-semibold text-orange-900">
                          {currentSlide?.tables.length || 0}
                        </div>
                        <div className="text-sm text-orange-600">Tables</div>
                      </div>
                    </div>
                  </div>

                  {/* Texts */}
                  {currentSlide?.texts && currentSlide.texts.length > 0 && (
                    <div className="bg-white rounded-lg shadow-sm p-6">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4">
                        Texts ({currentSlide.texts.length})
                      </h4>
                      <div className="space-y-3">
                        {currentSlide.texts.map((text, index) => (
                          <div key={index} className="border rounded-lg p-4 bg-gray-50">
                            <div className="font-medium text-gray-900 mb-2">
                              "{text.text}"
                            </div>
                            <div className="text-sm text-gray-500">
                              Position: ({text.x}, {text.y}) | Font: {text.fontFamily} | 
                              Size: {text.fontSize}px | Color: {text.color}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Images */}
                  {currentSlide?.images && currentSlide.images.length > 0 && (
                    <div className="bg-white rounded-lg shadow-sm p-6">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4">
                        Images ({currentSlide.images.length})
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {currentSlide.images.map((image, index) => (
                          <div key={index} className="border rounded-lg p-4 bg-gray-50">
                            <div className="aspect-video bg-white rounded mb-3 flex items-center justify-center overflow-hidden">
                              <img
                                src={image.src}
                                alt={`Image ${index + 1}`}
                                className="max-w-full max-h-full object-contain"
                                onError={(e) => {
                                  e.currentTarget.style.display = 'none';
                                  e.currentTarget.nextElementSibling?.classList.remove('hidden');
                                }}
                              />
                              <div className="hidden text-gray-400">
                                <ImageIcon className="w-12 h-12" />
                              </div>
                            </div>
                            <div className="text-sm text-gray-500">
                              Position: ({image.x}, {image.y}) | 
                              Size: {image.width}×{image.height}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Shapes */}
                  {currentSlide?.shapes && currentSlide.shapes.length > 0 && (
                    <div className="bg-white rounded-lg shadow-sm p-6">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4">
                        Shapes ({currentSlide.shapes.length})
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {currentSlide.shapes.map((shape, index) => (
                          <div key={index} className="border rounded-lg p-4 bg-gray-50">
                            <div className="aspect-square bg-white rounded mb-3 flex items-center justify-center">
                              <svg
                                width="100"
                                height="100"
                                viewBox={`0 0 ${shape.width} ${shape.height}`}
                                className="max-w-full max-h-full"
                              >
                                <g
                                  fill={shape.fill || "#FFFFFF"}
                                  stroke={shape.border?.includes("solid") ? shape.border.split(" ")[2] : "none"}
                                  strokeWidth={shape.border?.includes("solid") ? shape.border.split("px")[0] : "0"}
                                  dangerouslySetInnerHTML={{ __html: shape.svg }}
                                />
                              </svg>
                            </div>
                            <div className="text-sm text-gray-500">
                              Position: ({shape.x}, {shape.y}) | 
                              Size: {shape.width}×{shape.height}
                              {shape.text && (
                                <div className="mt-1 font-medium text-gray-700">
                                  Text: "{shape.text}"
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Tables */}
                  {currentSlide?.tables && currentSlide.tables.length > 0 && (
                    <div className="bg-white rounded-lg shadow-sm p-6">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4">
                        Tables ({currentSlide.tables.length})
                      </h4>
                      <div className="space-y-4">
                        {currentSlide.tables.map((table, index) => (
                          <div key={index} className="border rounded-lg p-4 bg-gray-50">
                            <div className="mb-3 text-sm text-gray-500">
                              Position: ({table.x}, {table.y}) | 
                              Size: {table.width}×{table.height} | 
                              Rows: {table.rows.length}
                            </div>
                            <div className="overflow-x-auto">
                              <table className="min-w-full border border-gray-300">
                                <tbody>
                                  {table.rows.map((row, rowIndex) => (
                                    <tr key={rowIndex}>
                                      {row.map((cell, cellIndex) => (
                                        <td
                                          key={cellIndex}
                                          className="border border-gray-300 px-3 py-2 text-sm"
                                        >
                                          {cell}
                                        </td>
                                      ))}
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                /* JSON View */
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    JSON Output
                  </h3>
                  <pre className="bg-gray-900 text-green-400 p-4 rounded-lg overflow-auto max-h-96 text-sm">
                    {JSON.stringify(currentSlide, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>
        )}

        {!parseResult && !isLoading && (
          <div className="bg-white rounded-lg shadow-sm p-12 text-center">
            <Upload className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              No PPTX file uploaded
            </h3>
            <p className="text-gray-600">
              Upload a PPTX file to see the enhanced parsing results
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

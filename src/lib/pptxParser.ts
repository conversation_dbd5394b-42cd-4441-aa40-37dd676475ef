import JSZip from "jszip";
import { parseStringPromise } from "xml2js";

// Updated interfaces according to specifications
export interface SlideText {
  text: string;
  x: number;
  y: number;
  fontSize?: number;
  fontFamily?: string;
  color?: string;
}

export interface SlideImage {
  src: string; // base64
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface SlideShape {
  svg: string; // SVG string, e.g., <rect ... /> or <g>...</g> for groups
  x: number;
  y: number;
  width: number;
  height: number;
  text?: string;
  fill?: string;
  border?: string;
}

export interface SlideTable {
  x: number;
  y: number;
  width: number;
  height: number;
  rows: string[][]; // Each element is a row
}

export interface PptxSlide {
  slideNumber: number;
  texts: SlideText[];
  images: SlideImage[];
  shapes: SlideShape[]; // includes both single shapes and group shapes (svg string)
  tables: SlideTable[];
}

export interface ExtractedImage {
  src: string; // base64 data URL
  name: string; // original filename
  path: string; // path in PPTX
  type: string; // image type (png, jpg, etc.)
}

export interface PptxParseResult {
  slides: PptxSlide[];
  images: ExtractedImage[]; // All extracted images
  metadata: {
    totalSlides: number;
    title?: string;
    author?: string;
    createdDate?: string;
  };
}

// Extract images from PPTX media folder
const extractImagesFromPptx = async (zip: JSZip): Promise<ExtractedImage[]> => {
  const images: ExtractedImage[] = [];

  try {
    // Find all files in ppt/media/ folder
    const mediaFiles = Object.keys(zip.files).filter(
      (path) => path.startsWith("ppt/media/") && !path.endsWith("/")
    );

    console.log("Found media files:", mediaFiles);

    for (const path of mediaFiles) {
      try {
        const file = zip.files[path];
        if (!file) continue;

        // Get file extension to determine image type
        const fileName = path.split("/").pop() || "";
        const fileExtension = fileName.split(".").pop()?.toLowerCase() || "";

        // Only process image files
        const imageExtensions = [
          "png",
          "jpg",
          "jpeg",
          "gif",
          "bmp",
          "svg",
          "webp",
        ];
        if (!imageExtensions.includes(fileExtension)) {
          console.log(`Skipping non-image file: ${path}`);
          continue;
        }

        // Convert to base64
        const fileData = await file.async("base64");
        const mimeType = getMimeType(fileExtension);

        images.push({
          src: `data:${mimeType};base64,${fileData}`,
          name: fileName,
          path: path,
          type: fileExtension,
        });

        console.log(`Extracted image: ${fileName} (${fileExtension})`);
      } catch (error) {
        console.error(`Error extracting image ${path}:`, error);
      }
    }
  } catch (error) {
    console.error("Error extracting images from PPTX:", error);
  }

  return images;
};

// Get MIME type for image extensions
const getMimeType = (extension: string): string => {
  const mimeTypes: { [key: string]: string } = {
    png: "image/png",
    jpg: "image/jpeg",
    jpeg: "image/jpeg",
    gif: "image/gif",
    bmp: "image/bmp",
    svg: "image/svg+xml",
    webp: "image/webp",
  };
  return mimeTypes[extension] || "image/png";
};

// Helper function to extract text from XML nodes
const extractTextFromNode = (node: any): string[] => {
  const texts: string[] = [];

  if (!node) return texts;

  // Handle different text structures in PPTX
  const processTextNode = (textNode: any) => {
    if (typeof textNode === "string") {
      texts.push(textNode);
    } else if (Array.isArray(textNode)) {
      textNode.forEach(processTextNode);
    } else if (textNode && typeof textNode === "object") {
      // Check for text content in various properties
      if (textNode._) {
        texts.push(textNode._);
      } else if (textNode.$text) {
        texts.push(textNode.$text);
      } else {
        // Recursively search for text in nested objects
        Object.values(textNode).forEach(processTextNode);
      }
    }
  };

  processTextNode(node);
  return texts;
};

// Generate SVG path for shape type
const generateSvgPath = (
  shapeType: string,
  width: number,
  height: number,
  childrenSvg?: string
): string => {
  const w = width;
  const h = height;

  switch (shapeType) {
    case "group":
      return `<g>${childrenSvg || ""}</g>`;

    case "roundRect":
      const rx = Math.min(w, h) * 0.1; // 10% radius
      return `<rect x="0" y="0" width="${w}" height="${h}" rx="${rx}" ry="${rx}" />`;

    case "triangle":
      return `<polygon points="${w / 2},0 ${w},${h} 0,${h}" />`;

    case "diamond":
      return `<polygon points="${w / 2},0 ${w},${h / 2} ${w / 2},${h} 0,${
        h / 2
      }" />`;

    case "rightArrow":
      return `<polygon points="0,${h * 0.3} ${w * 0.7},${h * 0.3} ${
        w * 0.7
      },0 ${w},${h / 2} ${w * 0.7},${h} ${w * 0.7},${h * 0.7} 0,${h * 0.7}" />`;

    case "leftArrow":
      return `<polygon points="0,${h / 2} ${w * 0.3},0 ${w * 0.3},${
        h * 0.3
      } ${w},${h * 0.3} ${w},${h * 0.7} ${w * 0.3},${h * 0.7} ${
        w * 0.3
      },${h}" />`;

    case "star5":
      const centerX = w / 2;
      const centerY = h / 2;
      const outerRadius = Math.min(w, h) / 2;
      const innerRadius = outerRadius * 0.4;
      let points = "";

      for (let i = 0; i < 10; i++) {
        const angle = (i * Math.PI) / 5 - Math.PI / 2;
        const radius = i % 2 === 0 ? outerRadius : innerRadius;
        const x = centerX + radius * Math.cos(angle);
        const y = centerY + radius * Math.sin(angle);
        points += `${x},${y} `;
      }

      return `<polygon points="${points.trim()}" />`;

    case "plus":
      return `<path d="M ${w * 0.4} 0 L ${w * 0.6} 0 L ${w * 0.6} ${
        h * 0.4
      } L ${w} ${h * 0.4} L ${w} ${h * 0.6} L ${w * 0.6} ${h * 0.6} L ${
        w * 0.6
      } ${h} L ${w * 0.4} ${h} L ${w * 0.4} ${h * 0.6} L 0 ${h * 0.6} L 0 ${
        h * 0.4
      } L ${w * 0.4} ${h * 0.4} Z" />`;

    case "heart":
      return `<path d="M ${w / 2} ${h * 0.8} C ${w / 2} ${h * 0.8} 0 ${
        h * 0.4
      } 0 ${h * 0.25} C 0 ${h * 0.1} ${w * 0.25} 0 ${w / 2} ${h * 0.3} C ${
        w * 0.75
      } 0 ${w} ${h * 0.1} ${w} ${h * 0.25} C ${w} ${h * 0.4} ${w / 2} ${
        h * 0.8
      } ${w / 2} ${h * 0.8} Z" />`;

    case "hexagon":
      const hexCenterX = w / 2;
      const hexCenterY = h / 2;
      const hexPoints: string[] = [];
      for (let i = 0; i < 6; i++) {
        const angle = (i * Math.PI) / 3;
        const x = hexCenterX + (w / 2) * Math.cos(angle);
        const y = hexCenterY + (h / 2) * Math.sin(angle);
        hexPoints.push(`${x},${y}`);
      }
      return `<polygon points="${hexPoints.join(" ")}" />`;

    case "flowChartProcess":
      return `<rect x="0" y="0" width="${w}" height="${h}" rx="4" ry="4" />`;

    case "flowChartDecision":
      return `<polygon points="${w / 2},0 ${w},${h / 2} ${w / 2},${h} 0,${
        h / 2
      }" />`;

    default: // rect
      return `<rect x="0" y="0" width="${w}" height="${h}" />`;
  }
};

// Enhanced parsing functions according to specifications

// Enhanced parsing functions according to specifications

// 1. Parse text from shape (p:sp)
const parseTextFromSp = (shape: any): string | null => {
  const textBody = shape["p:txBody"]?.[0];
  if (!textBody) return null;

  const paragraphs = textBody["a:p"] || [];
  const texts: string[] = [];

  paragraphs.forEach((p: any) => {
    const runs = p["a:r"] || [];
    runs.forEach((r: any) => {
      const text = r["a:t"]?.[0];
      if (text) texts.push(text);
    });
  });

  return texts.length > 0 ? texts.join(" ") : null;
};

// 2. Parse shape to SVG
const parseShapeToSvg = (shape: any): SlideShape | null => {
  const spPr = shape["p:spPr"]?.[0];
  if (!spPr) return null;

  const xfrm = spPr["a:xfrm"]?.[0];
  const off = xfrm?.["a:off"]?.[0]?.$;
  const ext = xfrm?.["a:ext"]?.[0]?.$;

  if (!off || !ext) return null;

  const x = Math.round(parseInt(off.x) / 12700);
  const y = Math.round(parseInt(off.y) / 12700);
  const width = Math.round(parseInt(ext.cx) / 12700);
  const height = Math.round(parseInt(ext.cy) / 12700);

  const prstGeom = spPr["a:prstGeom"]?.[0];
  const shapeType = prstGeom?.$?.prst || "rect";

  // Extract fill color
  let fill = "#FFFFFF";
  const solidFill = spPr["a:solidFill"]?.[0];
  if (solidFill) {
    const srgbClr = solidFill["a:srgbClr"]?.[0]?.$?.val;
    if (srgbClr) {
      fill = `#${srgbClr}`;
    }
  }

  // Extract border
  let border = "none";
  const ln = spPr["a:ln"]?.[0];
  if (ln) {
    const w = ln.$?.w ? Math.round(parseInt(ln.$.w) / 12700) : 1;
    const srgbClr = ln["a:solidFill"]?.[0]?.["a:srgbClr"]?.[0]?.$?.val;
    const borderColor = srgbClr ? `#${srgbClr}` : "#000000";
    border = `${w}px solid ${borderColor}`;
  }

  const svg = generateSvgPath(shapeType, width, height);

  // Extract text if present
  const text = parseTextFromSp(shape);

  return {
    svg,
    x,
    y,
    width,
    height,
    text: text || undefined,
    fill,
    border,
  };
};

// 3. Parse group shape
const parseGroupShape = (group: any): SlideShape | null => {
  const children: string[] = [];

  // Process child shapes
  const shapeList = group["p:sp"] || [];
  shapeList.forEach((sp: any) => {
    const shapeData = parseShapeToSvg(sp);
    if (shapeData) {
      children.push(
        `<g transform="translate(${shapeData.x},${shapeData.y})" fill="${shapeData.fill}">${shapeData.svg}</g>`
      );
    }
  });

  // Process child images (if any in group)
  const imageList = group["p:pic"] || [];
  imageList.forEach((pic: any) => {
    const imgData = parseImageElement(pic);
    if (imgData) {
      children.push(
        `<image x="${imgData.x}" y="${imgData.y}" width="${imgData.width}" height="${imgData.height}" href="${imgData.src}" />`
      );
    }
  });

  // Get group bounds
  const grpSpPr = group["p:grpSpPr"]?.[0];
  const xfrm = grpSpPr?.["a:xfrm"]?.[0];
  const off = xfrm?.["a:off"]?.[0]?.$;
  const ext = xfrm?.["a:ext"]?.[0]?.$;

  if (!off || !ext || children.length === 0) return null;

  const x = Math.round(parseInt(off.x) / 12700);
  const y = Math.round(parseInt(off.y) / 12700);
  const width = Math.round(parseInt(ext.cx) / 12700);
  const height = Math.round(parseInt(ext.cy) / 12700);

  return {
    svg: `<g>${children.join("")}</g>`,
    x,
    y,
    width,
    height,
  };
};

// 4. Parse image element
const parseImageElement = (pic: any): SlideImage | null => {
  const blipFill = pic["p:blipFill"]?.[0];
  const blip = blipFill?.["a:blip"]?.[0];
  const embedId = blip?.$?.["r:embed"];

  const xfrm = pic["p:spPr"]?.[0]?.["a:xfrm"]?.[0];
  const off = xfrm?.["a:off"]?.[0]?.$;
  const ext = xfrm?.["a:ext"]?.[0]?.$;

  if (!embedId || !off || !ext) return null;

  const x = Math.round(parseInt(off.x) / 12700);
  const y = Math.round(parseInt(off.y) / 12700);
  const width = Math.round(parseInt(ext.cx) / 12700);
  const height = Math.round(parseInt(ext.cy) / 12700);

  return {
    src: "", // Will be filled with base64 data later
    x,
    y,
    width,
    height,
  };
};

// 5. Parse table element
const parseTableElement = (frame: any): SlideTable | null => {
  const graphic = frame["a:graphic"]?.[0];
  const tbl = graphic?.["a:graphicData"]?.[0]?.["a:tbl"]?.[0];
  if (!tbl) return null;

  const rows = tbl["a:tr"] || [];
  const table: string[][] = [];

  rows.forEach((row: any) => {
    const cells = row["a:tc"] || [];
    const rowData: string[] = [];

    cells.forEach((cell: any) => {
      const txBody = cell["a:txBody"]?.[0];
      if (txBody) {
        const paragraphs = txBody["a:p"] || [];
        let cellText = "";
        paragraphs.forEach((paragraph: any) => {
          const runs = paragraph["a:r"] || [];
          runs.forEach((run: any) => {
            const text = run["a:t"]?.[0];
            if (text) cellText += text;
          });
        });
        rowData.push(cellText.trim());
      } else {
        rowData.push("");
      }
    });

    if (rowData.length > 0) {
      table.push(rowData);
    }
  });

  // Get table position
  const xfrm = frame["p:xfrm"]?.[0];
  const off = xfrm?.["a:off"]?.[0]?.$;
  const ext = xfrm?.["a:ext"]?.[0]?.$;

  const x = off ? Math.round(parseInt(off.x) / 12700) : 0;
  const y = off ? Math.round(parseInt(off.y) / 12700) : 0;
  const width = ext ? Math.round(parseInt(ext.cx) / 12700) : 0;
  const height = ext ? Math.round(parseInt(ext.cy) / 12700) : 0;

  return table.length > 0
    ? {
        x,
        y,
        width,
        height,
        rows: table,
      }
    : null;
};

// 6. Enhanced parseSlideXml function using new parsing methods
const parseSlideXmlEnhanced = (
  slideXml: any,
  extractedImages: ExtractedImage[]
): PptxSlide => {
  const shapeTree = slideXml["p:cSld"]?.["p:spTree"]?.[0];
  if (!shapeTree)
    return { slideNumber: 1, texts: [], images: [], shapes: [], tables: [] };

  const slide: PptxSlide = {
    slideNumber: 1, // Will be set by caller
    texts: [],
    images: [],
    shapes: [],
    tables: [],
  };

  // Process individual shapes (p:sp)
  const shapes = shapeTree["p:sp"] || [];
  shapes.forEach((shape: any) => {
    // Extract text
    const text = parseTextFromSp(shape);
    if (text) {
      const spPr = shape["p:spPr"]?.[0];
      const xfrm = spPr?.["a:xfrm"]?.[0];
      const off = xfrm?.["a:off"]?.[0]?.$;

      const x = off ? Math.round(parseInt(off.x) / 12700) : 0;
      const y = off ? Math.round(parseInt(off.y) / 12700) : 0;

      slide.texts.push({
        text,
        x,
        y,
        fontSize: 12, // Default, could be extracted from run properties
        fontFamily: "Arial", // Default
        color: "#000000", // Default
      });
    }

    // Extract shape
    const shapeData = parseShapeToSvg(shape);
    if (shapeData) {
      slide.shapes.push(shapeData);
    }
  });

  // Process group shapes (p:grpSp)
  const groupShapes = shapeTree["p:grpSp"] || [];
  groupShapes.forEach((group: any) => {
    const groupData = parseGroupShape(group);
    if (groupData) {
      slide.shapes.push(groupData);
    }
  });

  // Process images (p:pic)
  const pictures = shapeTree["p:pic"] || [];
  pictures.forEach((pic: any, index: number) => {
    const imgData = parseImageElement(pic);
    if (imgData) {
      // Map to actual extracted image
      const actualImage = extractedImages[index] || null;
      slide.images.push({
        ...imgData,
        src: actualImage ? actualImage.src : "/images/placeholder-image.svg",
      });
    }
  });

  // Process tables (p:graphicFrame)
  const graphicFrames = shapeTree["p:graphicFrame"] || [];
  graphicFrames.forEach((frame: any) => {
    const tableData = parseTableElement(frame);
    if (tableData) {
      slide.tables.push(tableData);
    }
  });

  return slide;
};

// Enhanced image mapping with relationship parsing
const mapImagesToSlides = async (
  zip: JSZip,
  extractedImages: ExtractedImage[]
): Promise<Map<string, ExtractedImage>> => {
  const imageMap = new Map<string, ExtractedImage>();

  try {
    // Parse slide relationships to map embed IDs to actual images
    const slideFiles = Object.keys(zip.files).filter(
      (fileName) =>
        fileName.startsWith("ppt/slides/slide") &&
        fileName.endsWith(".xml") &&
        !fileName.includes("_rels")
    );

    for (const slideFile of slideFiles) {
      const relsFile = slideFile.replace(".xml", ".xml.rels");
      const relsPath = `ppt/slides/_rels/${relsFile.split("/").pop()}`;

      if (zip.files[relsPath]) {
        const relsXml = await zip.files[relsPath].async("string");
        const relsResult = await parseStringPromise(relsXml);

        const relationships =
          relsResult?.["Relationships"]?.["Relationship"] || [];
        relationships.forEach((rel: any) => {
          if (rel.$?.Type?.includes("image")) {
            const embedId = rel.$.Id;
            const target = rel.$.Target;

            // Find matching extracted image
            const matchingImage = extractedImages.find((img) =>
              img.path.includes(target.replace("../media/", ""))
            );

            if (matchingImage) {
              imageMap.set(embedId, matchingImage);
            }
          }
        });
      }
    }
  } catch (error) {
    console.warn("Error mapping images to slides:", error);
  }

  return imageMap;
};

// Extract position from transform
const extractPosition = (spPr: any): { x: number; y: number } => {
  try {
    const xfrm = spPr?.["a:xfrm"]?.[0];
    const off = xfrm?.["a:off"]?.[0]?.$;

    if (off) {
      return {
        x: Math.round(parseInt(off.x || "0") / 12700),
        y: Math.round(parseInt(off.y || "0") / 12700),
      };
    }
  } catch (err) {
    console.warn("Error extracting position:", err);
  }

  return { x: 0, y: 0 };
};

// Extract text from paragraph runs
const extractTextFromRuns = (runs: any[]): string => {
  if (!Array.isArray(runs)) return "";

  const textParts: string[] = [];

  runs.forEach((run) => {
    if (run["a:t"]) {
      const textNodes = Array.isArray(run["a:t"]) ? run["a:t"] : [run["a:t"]];
      textNodes.forEach((textNode) => {
        if (typeof textNode === "string") {
          textParts.push(textNode);
        } else if (textNode && textNode._) {
          textParts.push(textNode._);
        }
      });
    }
  });

  return textParts.join("");
};

// Parse individual slide XML with image mapping
const parseSlideXml = async (
  slideXml: string,
  slideNumber: number,
  extractedImages: ExtractedImage[] = []
): Promise<PptxSlide> => {
  try {
    const result = await parseStringPromise(slideXml);

    // Navigate through the slide structure
    const slideData = result?.["p:sld"];
    if (!slideData) {
      return {
        slideNumber,
        texts: [],
        images: [],
        shapes: [],
        tables: [],
      };
    }

    const commonSlideData = slideData["p:cSld"]?.[0];
    if (!commonSlideData) {
      return {
        slideNumber,
        texts: [],
        images: [],
        shapes: [],
        tables: [],
      };
    }

    // Use enhanced parsing method
    const slide = parseSlideXmlEnhanced(commonSlideData, extractedImages);
    slide.slideNumber = slideNumber;

    console.log(
      `Processed slide ${slideNumber}: ${slide.texts.length} texts, ${slide.images.length} images, ${slide.shapes.length} shapes, ${slide.tables.length} tables`
    );

    return slide;
  } catch (error) {
    console.error(`Error parsing slide ${slideNumber}:`, error);
    return {
      slideNumber,
      texts: [],
      images: [],
      shapes: [],
      tables: [],
    };
  }
};

// Main parser function
export const parsePptx = async (
  arrayBuffer: ArrayBuffer
): Promise<PptxParseResult> => {
  try {
    const zip = await JSZip.loadAsync(arrayBuffer);
    const slides: PptxSlide[] = [];

    // Extract all images from media folder
    console.log("Extracting images from PPTX...");
    const extractedImages = await extractImagesFromPptx(zip);
    console.log(`Extracted ${extractedImages.length} images`);

    // Get all slide files
    const slideFiles = Object.keys(zip.files)
      .filter(
        (fileName) =>
          fileName.startsWith("ppt/slides/slide") &&
          fileName.endsWith(".xml") &&
          !fileName.includes("_rels")
      )
      .sort((a, b) => {
        // Sort by slide number
        const aNum = parseInt(a.match(/slide(\d+)\.xml$/)?.[1] || "0");
        const bNum = parseInt(b.match(/slide(\d+)\.xml$/)?.[1] || "0");
        return aNum - bNum;
      });

    console.log("Found slide files:", slideFiles);

    // Parse each slide
    for (let i = 0; i < slideFiles.length; i++) {
      const fileName = slideFiles[i];
      const slideNumber = i + 1;

      try {
        const slideXml = await zip.files[fileName].async("string");
        const slide = await parseSlideXml(
          slideXml,
          slideNumber,
          extractedImages
        );
        slides.push(slide);
      } catch (error) {
        console.error(`Error processing slide ${slideNumber}:`, error);
        // Add empty slide to maintain numbering
        slides.push({
          slideNumber,
          texts: [],
          images: [],
          shapes: [],
          tables: [],
        });
      }
    }

    // Extract metadata from core properties if available
    let metadata = {
      totalSlides: slides.length,
      title: undefined as string | undefined,
      author: undefined as string | undefined,
      createdDate: undefined as string | undefined,
    };

    try {
      const corePropsFile = zip.files["docProps/core.xml"];
      if (corePropsFile) {
        const corePropsXml = await corePropsFile.async("string");
        const coreProps = await parseStringPromise(corePropsXml);

        metadata.title =
          coreProps?.["cp:coreProperties"]?.["dc:title"]?.[0] || undefined;
        metadata.author =
          coreProps?.["cp:coreProperties"]?.["dc:creator"]?.[0] || undefined;
        metadata.createdDate =
          coreProps?.["cp:coreProperties"]?.["dcterms:created"]?.[0]?._ ||
          undefined;
      }
    } catch (error) {
      console.warn("Could not extract metadata:", error);
    }

    return {
      slides,
      images: extractedImages,
      metadata,
    };
  } catch (error) {
    console.error("Error parsing PPTX file:", error);
    throw new Error(
      "Không thể đọc file PPTX. Vui lòng kiểm tra định dạng file."
    );
  }
};

// Utility function to extract all text from slides
export const extractAllText = (slides: PptxSlide[]): string => {
  return slides
    .map((slide) => slide.texts.map((t) => t.text).join(" "))
    .filter((text) => text.trim().length > 0)
    .join("\n\n");
};

// Utility function to get slide statistics
export const getSlideStatistics = (slides: PptxSlide[]) => {
  return {
    totalSlides: slides.length,
    totalTexts: slides.reduce((total, slide) => total + slide.texts.length, 0),
    totalImages: slides.reduce(
      (total, slide) => total + slide.images.length,
      0
    ),
    totalShapes: slides.reduce(
      (total, slide) => total + slide.shapes.length,
      0
    ),
    averageTextsPerSlide:
      slides.length > 0
        ? Math.round(
            (slides.reduce((total, slide) => total + slide.texts.length, 0) /
              slides.length) *
              10
          ) / 10
        : 0,
  };
};
